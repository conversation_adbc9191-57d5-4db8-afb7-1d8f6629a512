<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字符变形测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #000;
            color: #fff;
            overflow-x: hidden;
        }

        .page {
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }

        .page:nth-child(1) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .page:nth-child(2) {
            background: #f43a47;
        }

        .title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 2rem;
        }

        .subtitle {
            font-size: 1.5rem;
            opacity: 0.8;
        }

        /* 字符变形效果 */
        .morphing-char {
            display: inline-block;
            transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            transform-origin: center;
            will-change: transform, color;
        }

        /* 字符动画效果 */
        @keyframes charFloat {
            0%, 100% { 
                transform: translateY(0px) scale(1) rotate(0deg); 
            }
            25% { 
                transform: translateY(-10px) scale(1.1) rotate(2deg); 
                color: #ff6b6b;
            }
            50% { 
                transform: translateY(-15px) scale(1.2) rotate(-2deg); 
                color: #f43a47;
            }
            75% { 
                transform: translateY(-10px) scale(1.1) rotate(1deg); 
                color: #ff6b6b;
            }
        }

        @keyframes charWave {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg); 
            }
            50% { 
                transform: translateY(-20px) rotate(360deg); 
                color: #fff;
            }
        }

        /* 第1页字符效果 */
        #page1-text .morphing-char {
            animation: charFloat 2s ease-in-out infinite;
        }

        #page1-text .morphing-char:nth-child(1) { animation-delay: 0s; }
        #page1-text .morphing-char:nth-child(2) { animation-delay: 0.2s; }
        #page1-text .morphing-char:nth-child(3) { animation-delay: 0.4s; }
        #page1-text .morphing-char:nth-child(4) { animation-delay: 0.6s; }
        #page1-text .morphing-char:nth-child(5) { animation-delay: 0.8s; }
        #page1-text .morphing-char:nth-child(6) { animation-delay: 1s; }
        #page1-text .morphing-char:nth-child(7) { animation-delay: 1.2s; }
        #page1-text .morphing-char:nth-child(8) { animation-delay: 1.4s; }

        /* 第2页字符效果 */
        #page2-text .morphing-char {
            animation: charWave 3s ease-in-out infinite;
            color: #000;
        }

        #page2-text .morphing-char:nth-child(1) { animation-delay: 0s; }
        #page2-text .morphing-char:nth-child(2) { animation-delay: 0.3s; }
        #page2-text .morphing-char:nth-child(3) { animation-delay: 0.6s; }
        #page2-text .morphing-char:nth-child(4) { animation-delay: 0.9s; }
        #page2-text .morphing-char:nth-child(5) { animation-delay: 1.2s; }
        #page2-text .morphing-char:nth-child(6) { animation-delay: 1.5s; }
        #page2-text .morphing-char:nth-child(7) { animation-delay: 1.8s; }
        #page2-text .morphing-char:nth-child(8) { animation-delay: 2.1s; }
        #page2-text .morphing-char:nth-child(9) { animation-delay: 2.4s; }
        #page2-text .morphing-char:nth-child(10) { animation-delay: 2.7s; }
        #page2-text .morphing-char:nth-child(11) { animation-delay: 3s; }
    </style>
</head>
<body>
    <!-- 第1页 -->
    <div class="page">
        <div class="title" id="page1-text">CREATING</div>
        <div class="subtitle">字符浮动效果</div>
    </div>

    <!-- 第2页 -->
    <div class="page">
        <div class="title" id="page2-text">COLLABORATE</div>
        <div class="subtitle">字符旋转效果</div>
    </div>

    <script>
        // 创建字符元素
        function createCharacterElements(container) {
            const text = container.textContent;
            container.innerHTML = '';
            
            text.split('').forEach((char, index) => {
                const charElement = document.createElement('span');
                charElement.className = 'morphing-char';
                charElement.textContent = char === ' ' ? '\u00A0' : char;
                charElement.style.animationDelay = `${index * 0.1}s`;
                container.appendChild(charElement);
            });
            
            console.log(`创建了 ${text.length} 个字符元素`);
        }

        // 文字轮换功能
        function startTextRotation(element, textArray, interval) {
            let currentIndex = 0;
            
            setInterval(() => {
                const chars = element.querySelectorAll('.morphing-char');
                
                // 字符淡出动画
                chars.forEach((char, index) => {
                    setTimeout(() => {
                        char.style.opacity = '0';
                        char.style.transform = 'translateY(-20px) scale(0.8)';
                    }, index * 50);
                });
                
                // 切换文字内容
                setTimeout(() => {
                    currentIndex = (currentIndex + 1) % textArray.length;
                    const newText = textArray[currentIndex];
                    
                    // 重新创建字符元素
                    element.innerHTML = '';
                    newText.split('').forEach((char, index) => {
                        const charElement = document.createElement('span');
                        charElement.className = 'morphing-char';
                        charElement.textContent = char === ' ' ? '\u00A0' : char;
                        charElement.style.opacity = '0';
                        charElement.style.transform = 'translateY(20px) scale(0.8)';
                        element.appendChild(charElement);
                        
                        // 字符淡入动画
                        setTimeout(() => {
                            charElement.style.opacity = '1';
                            charElement.style.transform = 'translateY(0) scale(1)';
                        }, index * 80);
                    });
                }, chars.length * 50 + 200);
                
            }, interval);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始初始化字符变形...');
            
            // 第1页
            const page1Element = document.getElementById('page1-text');
            if (page1Element) {
                createCharacterElements(page1Element);
                const page1Texts = ['CREATING', 'BUILDING', 'DESIGNING', 'CODING'];
                startTextRotation(page1Element, page1Texts, 4000);
            }
            
            // 第2页
            const page2Element = document.getElementById('page2-text');
            if (page2Element) {
                createCharacterElements(page2Element);
                const page2Texts = ['COLLABORATE', 'CONNECT', 'CREATE', 'COMMUNICATE'];
                startTextRotation(page2Element, page2Texts, 3500);
            }
            
            console.log('字符变形初始化完成！');
        });
    </script>
</body>
</html>
